HELP.md
.gradle/
.kotlin/
target/
build/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
!**/src/test/resources/output/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea/*
!.idea/codeStyles/*
*.iws
*.iml
*.ipr

# Node
node_modules
npm-debug.log
yarn-error.log

# Miscellaneous
.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings
/docker/postgres
/docker/volume
.terraform

**/output/*.pdf
**/output/*.png
**/.openapi-generator/**
acceptance-tests/report/**
screenshots
*.DS_Store

import org.springframework.boot.gradle.tasks.run.BootRun

plugins {
    alias(libs.plugins.gradle.jib)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.gradle.docker.compose)
    alias(libs.plugins.gradle.jaxb)
}

apply(plugin = "org.springframework.boot")
apply(plugin = "io.spring.dependency-management")

dependencies {
    implementation(project(":common-utils"))
    implementation(project(":domain-model-api"))
    implementation(project(":domain-logic-api"))
    implementation(project(":databasefiller"))
    implementation(project(":common-network"))
    implementation(project(":api-outgoing-notification-client"))
    implementation(project(":api-external-server"))
    implementation(project(":api-webportal-server"))

    runtimeOnly(project(":workflow-api"))
    runtimeOnly(project(":workflow"))

    implementation(libs.spring.boot.starter.data.r2dbc)
    implementation(libs.spring.boot.starter.webflux)
    implementation(libs.spring.boot.starter.validation)
    implementation(libs.spring.boot.starter.cache)
    implementation(libs.spring.boot.starter.security)
    implementation(libs.spring.boot.starter.actuator)
    implementation(libs.spring.boot.starter.data.jdbc)
    implementation(libs.org.springframework.retry.spring.retry)

    // jaxb
    implementation(libs.jakarta.xml.bind.api)
    implementation(libs.jaxb.runtime)
    implementation(libs.jakarta.activation.api)

    implementation(libs.swift.mt)

    implementation(libs.jackson.module.kotlin)
    implementation(libs.bundles.impl.network)
    implementation(libs.bundles.impl.shedlock)
    implementation(libs.bundles.impl.coroutines)

    implementation(libs.org.liquibase.liquibase.core)
    implementation(libs.postgresql)
    implementation(libs.r2dbc.pool)
    implementation(libs.r2dbc.postgresql)
    implementation(libs.org.openapitools.jackson.databind.nullable)
    implementation(libs.io.swagger.core.v3.swagger.annotations)
    implementation(libs.caffeine)

    implementation(libs.jjwt.api)
    runtimeOnly(libs.jjwt.impl)
    runtimeOnly(libs.jjwt.jackson)

    testImplementation(project(":common-test"))
}

val useArm64 = project.findProperty("useArm64")?.toString()?.toBoolean() ?: false

jib {
    from {
        image = "amazoncorretto:21-al2023"

        if (useArm64) {
            platforms {
                platform {
                    architecture = "arm64"
                    os = "linux"
                }
            }
        }
    }
    to {
        image = "234542553877.dkr.ecr.eu-central-1.amazonaws.com/frontend"
        tags = setOf("latest")
    }
    container {
        jvmFlags = listOf("-XX:MaxRAMPercentage=85")
        ports = listOf("8080")
        environment = mapOf("JAVA_TOOL_OPTIONS" to "-Dspring.profiles.active=dev,demo")
    }
}

tasks.withType<BootRun> {
    dependsOn("composeUp")
}

dockerCompose {
    useComposeFiles = listOf("../docker/docker-compose.yml")
}

// jaxb {
//    javaGen {
//        register("camt53V02") {
//            schema = File("$projectDir/src/main/resources/xsd/camt/camt.053.001.02.xsd")
//            outputDir = File("$projectDir/src/main/java")
//            packageName = "com.klosesoft.billingsolution.frontend.generated.model.camt53.v02"
//            header = false
//        }
//    }
// }

tasks.jar {
    isPreserveFileTimestamps = false
    isReproducibleFileOrder = true
}
